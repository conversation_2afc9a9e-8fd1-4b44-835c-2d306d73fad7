package com.siact.control.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 网关锁管理器
 * 为每个网关提供独立的锁机制，确保同一网关的请求串行执行，不同网关的请求可以并行执行
 * 
 * <AUTHOR>
 * @create 2024/8/11
 */
@Slf4j
@Component
public class GatewayLockManager {
    
    /**
     * 网关锁映射表，key为网关编号，value为对应的锁
     */
    private final ConcurrentHashMap<String, ReentrantLock> gatewayLocks = new ConcurrentHashMap<>();
    
    /**
     * 锁的引用计数，用于清理不再使用的锁
     */
    private final ConcurrentHashMap<String, Integer> lockRefCount = new ConcurrentHashMap<>();
    
    /**
     * 默认锁等待超时时间（秒）
     */
    private static final int DEFAULT_LOCK_TIMEOUT = 30;
    
    /**
     * 获取指定网关的锁
     * 
     * @param gatewayCode 网关编号
     * @return 网关对应的锁
     */
    private ReentrantLock getLock(String gatewayCode) {
        return gatewayLocks.computeIfAbsent(gatewayCode, k -> {
            log.debug("为网关 {} 创建新的锁", gatewayCode);
            return new ReentrantLock(true); // 使用公平锁，确保按请求顺序处理
        });
    }
    
    /**
     * 增加锁的引用计数
     * 
     * @param gatewayCode 网关编号
     */
    private void incrementRefCount(String gatewayCode) {
        lockRefCount.compute(gatewayCode, (k, v) -> v == null ? 1 : v + 1);
    }
    
    /**
     * 减少锁的引用计数，如果计数为0则清理锁
     * 
     * @param gatewayCode 网关编号
     */
    private void decrementRefCount(String gatewayCode) {
        lockRefCount.compute(gatewayCode, (k, v) -> {
            if (v == null || v <= 1) {
                // 引用计数为0，清理锁
                gatewayLocks.remove(gatewayCode);
                log.debug("清理网关 {} 的锁", gatewayCode);
                return null;
            }
            return v - 1;
        });
    }
    
    /**
     * 在网关锁保护下执行操作（使用默认超时时间）
     * 
     * @param gatewayCode 网关编号
     * @param operation 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     * @throws RuntimeException 如果获取锁超时或操作执行失败
     */
    public <T> T executeWithLock(String gatewayCode, Supplier<T> operation) {
        return executeWithLock(gatewayCode, operation, DEFAULT_LOCK_TIMEOUT);
    }
    
    /**
     * 在网关锁保护下执行操作
     * 
     * @param gatewayCode 网关编号
     * @param operation 要执行的操作
     * @param timeoutSeconds 锁等待超时时间（秒）
     * @param <T> 返回值类型
     * @return 操作结果
     * @throws RuntimeException 如果获取锁超时或操作执行失败
     */
    public <T> T executeWithLock(String gatewayCode, Supplier<T> operation, int timeoutSeconds) {
        if (gatewayCode == null || gatewayCode.trim().isEmpty()) {
            throw new IllegalArgumentException("网关编号不能为空");
        }
        
        ReentrantLock lock = getLock(gatewayCode);
        incrementRefCount(gatewayCode);
        
        boolean lockAcquired = false;
        long startTime = System.currentTimeMillis();
        
        try {
            // 尝试获取锁
            lockAcquired = lock.tryLock(timeoutSeconds, TimeUnit.SECONDS);
            
            if (!lockAcquired) {
                log.error("获取网关锁超时 - 网关: {}, 超时时间: {}秒", gatewayCode, timeoutSeconds);
                throw new RuntimeException("获取网关锁超时，网关: " + gatewayCode);
            }
            
            long lockWaitTime = System.currentTimeMillis() - startTime;
            if (lockWaitTime > 1000) { // 如果等待时间超过1秒，记录日志
                log.info("获取网关锁成功 - 网关: {}, 等待时间: {}ms", gatewayCode, lockWaitTime);
            }
            
            // 执行操作
            long operationStartTime = System.currentTimeMillis();
            T result = operation.get();
            long operationTime = System.currentTimeMillis() - operationStartTime;
            
            log.debug("网关操作完成 - 网关: {}, 执行时间: {}ms", gatewayCode, operationTime);
            return result;
            
        } catch (InterruptedException e) {
            log.error("等待网关锁被中断 - 网关: {}", gatewayCode, e);
            Thread.currentThread().interrupt();
            throw new RuntimeException("等待网关锁被中断，网关: " + gatewayCode, e);
        } catch (Exception e) {
            log.error("执行网关操作失败 - 网关: {}", gatewayCode, e);
            throw new RuntimeException("执行网关操作失败，网关: " + gatewayCode, e);
        } finally {
            if (lockAcquired) {
                lock.unlock();
                log.debug("释放网关锁 - 网关: {}", gatewayCode);
            }
            decrementRefCount(gatewayCode);
        }
    }
    
    /**
     * 检查指定网关是否有锁正在使用
     * 
     * @param gatewayCode 网关编号
     * @return 是否有锁正在使用
     */
    public boolean isLocked(String gatewayCode) {
        ReentrantLock lock = gatewayLocks.get(gatewayCode);
        return lock != null && lock.isLocked();
    }
    
    /**
     * 获取指定网关的等待队列长度
     * 
     * @param gatewayCode 网关编号
     * @return 等待队列长度
     */
    public int getQueueLength(String gatewayCode) {
        ReentrantLock lock = gatewayLocks.get(gatewayCode);
        return lock != null ? lock.getQueueLength() : 0;
    }
    
    /**
     * 获取当前管理的网关锁数量
     * 
     * @return 网关锁数量
     */
    public int getLockCount() {
        return gatewayLocks.size();
    }
    
    /**
     * 获取网关锁的统计信息
     * 
     * @return 统计信息字符串
     */
    public String getStatistics() {
        StringBuilder stats = new StringBuilder();
        stats.append("网关锁统计信息:\n");
        stats.append("总锁数量: ").append(gatewayLocks.size()).append("\n");
        
        gatewayLocks.forEach((gateway, lock) -> {
            stats.append("网关: ").append(gateway)
                 .append(", 是否锁定: ").append(lock.isLocked())
                 .append(", 等待队列: ").append(lock.getQueueLength())
                 .append(", 引用计数: ").append(lockRefCount.getOrDefault(gateway, 0))
                 .append("\n");
        });
        
        return stats.toString();
    }
    
    /**
     * 强制清理所有锁（仅用于测试或紧急情况）
     */
    public void clearAllLocks() {
        log.warn("强制清理所有网关锁");
        gatewayLocks.clear();
        lockRefCount.clear();
    }
}
