package com.siact.control.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 网关锁管理器
 * 为每个网关提供独立的锁机制，确保同一网关的请求串行执行，不同网关的请求可以并行执行
 *
 * <AUTHOR>
 * @create 2024/8/11
 */
@Slf4j
@Component
public class GatewayLockManager {

    /**
     * 网关锁映射表
     */
    private final ConcurrentHashMap<String, ReentrantLock> gatewayLocks = new ConcurrentHashMap<>();

    /**
     * 默认锁等待超时时间（秒）
     */
    private static final int DEFAULT_LOCK_TIMEOUT = 30;

    /**
     * 获取指定网关的锁
     */
    private ReentrantLock getLock(String gatewayCode) {
        return gatewayLocks.computeIfAbsent(gatewayCode, k -> new ReentrantLock(true));
    }
    

    
    /**
     * 在网关锁保护下执行操作
     *
     * @param gatewayCode 网关编号
     * @param operation 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     */
    public <T> T executeWithLock(String gatewayCode, Supplier<T> operation) {
        if (gatewayCode == null || gatewayCode.trim().isEmpty()) {
            throw new IllegalArgumentException("网关编号不能为空");
        }

        ReentrantLock lock = getLock(gatewayCode);
        long startTime = System.currentTimeMillis();

        try {
            // 尝试获取锁
            if (!lock.tryLock(DEFAULT_LOCK_TIMEOUT, TimeUnit.SECONDS)) {
                log.error("网关锁获取超时 - 网关: {}", gatewayCode);
                throw new RuntimeException("获取网关锁超时，网关: " + gatewayCode);
            }

            long lockWaitTime = System.currentTimeMillis() - startTime;
            if (lockWaitTime > 1000) {
                log.warn("网关锁等待时间较长 - 网关: {}, 等待时间: {}ms", gatewayCode, lockWaitTime);
            }

            // 执行操作
            return operation.get();

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("等待网关锁被中断，网关: " + gatewayCode, e);
        } catch (Exception e) {
            log.error("网关操作执行失败 - 网关: {}", gatewayCode, e);
            throw new RuntimeException("执行网关操作失败，网关: " + gatewayCode, e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
    
    /**
     * 检查指定网关是否有锁正在使用
     */
    public boolean isLocked(String gatewayCode) {
        ReentrantLock lock = gatewayLocks.get(gatewayCode);
        return lock != null && lock.isLocked();
    }

    /**
     * 获取网关锁的统计信息
     */
    public String getStatistics() {
        StringBuilder stats = new StringBuilder();
        stats.append("网关锁统计 - 总数量: ").append(gatewayLocks.size()).append("\n");

        gatewayLocks.forEach((gateway, lock) -> {
            stats.append("网关: ").append(gateway)
                 .append(", 锁定: ").append(lock.isLocked())
                 .append(", 等待: ").append(lock.getQueueLength())
                 .append("\n");
        });

        return stats.toString();
    }
}
