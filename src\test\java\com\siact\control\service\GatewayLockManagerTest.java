package com.siact.control.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 网关锁管理器测试类
 *
 * <AUTHOR>
 * @create 2024/8/11
 */
@Slf4j
@SpringBootTest
public class GatewayLockManagerTest {

    private GatewayLockManager gatewayLockManager;

    @BeforeEach
    void setUp() {
        gatewayLockManager = new GatewayLockManager();
    }
    
    /**
     * 测试基本的锁功能
     */
    @Test
    void testBasicLockFunctionality() {
        String gatewayCode = "GATEWAY_001";
        AtomicInteger counter = new AtomicInteger(0);

        // 执行一个简单的操作
        Integer result = gatewayLockManager.executeWithLock(gatewayCode, () -> {
            counter.incrementAndGet();
            return counter.get();
        });

        assertEquals(1, result);
        assertEquals(1, counter.get());
        log.info("基本锁功能测试通过");
    }

    /**
     * 测试同一网关的串行执行
     */
    @Test
    void testSerialExecutionForSameGateway() throws InterruptedException {
        String gatewayCode = "GATEWAY_001";
        int threadCount = 3;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        AtomicInteger counter = new AtomicInteger(0);

        // 启动多个线程同时访问同一网关
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    gatewayLockManager.executeWithLock(gatewayCode, () -> {
                        int currentValue = counter.incrementAndGet();

                        // 模拟处理时间
                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }

                        return currentValue;
                    });
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        assertTrue(latch.await(10, TimeUnit.SECONDS));

        // 验证执行是串行的
        assertEquals(threadCount, counter.get());

        executor.shutdown();
        log.info("串行执行测试通过");
    }
    
    /**
     * 测试异常处理
     */
    @Test
    void testExceptionHandling() {
        String gatewayCode = "GATEWAY_001";

        // 测试空网关编号
        assertThrows(IllegalArgumentException.class, () -> {
            gatewayLockManager.executeWithLock(null, () -> "result");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            gatewayLockManager.executeWithLock("", () -> "result");
        });

        // 测试操作中抛出异常
        assertThrows(RuntimeException.class, () -> {
            gatewayLockManager.executeWithLock(gatewayCode, () -> {
                throw new RuntimeException("测试异常");
            });
        });

        log.info("异常处理测试通过");
    }
}
