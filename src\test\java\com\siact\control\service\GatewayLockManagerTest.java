package com.siact.control.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 网关锁管理器测试类
 * 
 * <AUTHOR>
 * @create 2024/8/11
 */
@Slf4j
@SpringBootTest
public class GatewayLockManagerTest {
    
    private GatewayLockManager gatewayLockManager;
    
    @BeforeEach
    void setUp() {
        gatewayLockManager = new GatewayLockManager();
    }
    
    /**
     * 测试基本的锁功能
     */
    @Test
    void testBasicLockFunctionality() {
        String gatewayCode = "GATEWAY_001";
        AtomicInteger counter = new AtomicInteger(0);
        
        // 执行一个简单的操作
        Integer result = gatewayLockManager.executeWithLock(gatewayCode, () -> {
            counter.incrementAndGet();
            return counter.get();
        });
        
        assertEquals(1, result);
        assertEquals(1, counter.get());
    }
    
    /**
     * 测试同一网关的串行执行
     */
    @Test
    void testSerialExecutionForSameGateway() throws InterruptedException {
        String gatewayCode = "GATEWAY_001";
        int threadCount = 5;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        List<Integer> executionOrder = new ArrayList<>();
        AtomicInteger counter = new AtomicInteger(0);
        
        // 启动多个线程同时访问同一网关
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    gatewayLockManager.executeWithLock(gatewayCode, () -> {
                        int currentValue = counter.incrementAndGet();
                        synchronized (executionOrder) {
                            executionOrder.add(threadId);
                        }
                        
                        // 模拟一些处理时间
                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                        
                        return currentValue;
                    });
                } finally {
                    latch.countDown();
                }
            });
        }
        
        // 等待所有线程完成
        assertTrue(latch.await(10, TimeUnit.SECONDS));
        
        // 验证执行顺序是串行的
        assertEquals(threadCount, executionOrder.size());
        assertEquals(threadCount, counter.get());
        
        executor.shutdown();
    }
    
    /**
     * 测试不同网关的并行执行
     */
    @Test
    void testParallelExecutionForDifferentGateways() throws InterruptedException {
        int gatewayCount = 3;
        int threadsPerGateway = 2;
        int totalThreads = gatewayCount * threadsPerGateway;
        
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch finishLatch = new CountDownLatch(totalThreads);
        ExecutorService executor = Executors.newFixedThreadPool(totalThreads);
        
        ConcurrentHashMap<String, List<Long>> gatewayExecutionTimes = new ConcurrentHashMap<>();
        
        // 为每个网关启动多个线程
        for (int g = 0; g < gatewayCount; g++) {
            String gatewayCode = "GATEWAY_" + String.format("%03d", g + 1);
            gatewayExecutionTimes.put(gatewayCode, new ArrayList<>());
            
            for (int t = 0; t < threadsPerGateway; t++) {
                executor.submit(() -> {
                    try {
                        // 等待所有线程准备就绪
                        startLatch.await();
                        
                        long startTime = System.currentTimeMillis();
                        gatewayLockManager.executeWithLock(gatewayCode, () -> {
                            // 模拟处理时间
                            try {
                                Thread.sleep(200);
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                            }
                            return null;
                        });
                        long endTime = System.currentTimeMillis();
                        
                        synchronized (gatewayExecutionTimes.get(gatewayCode)) {
                            gatewayExecutionTimes.get(gatewayCode).add(endTime - startTime);
                        }
                        
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    } finally {
                        finishLatch.countDown();
                    }
                });
            }
        }
        
        // 启动所有线程
        startLatch.countDown();
        
        // 等待所有线程完成
        assertTrue(finishLatch.await(15, TimeUnit.SECONDS));
        
        // 验证每个网关都有正确数量的执行记录
        for (String gatewayCode : gatewayExecutionTimes.keySet()) {
            List<Long> times = gatewayExecutionTimes.get(gatewayCode);
            assertEquals(threadsPerGateway, times.size());
            log.info("网关 {} 执行时间: {}", gatewayCode, times);
        }
        
        executor.shutdown();
    }
    
    /**
     * 测试锁超时功能
     */
    @Test
    void testLockTimeout() throws InterruptedException {
        String gatewayCode = "GATEWAY_001";
        CountDownLatch latch = new CountDownLatch(2);
        ExecutorService executor = Executors.newFixedThreadPool(2);
        
        // 第一个线程持有锁较长时间
        executor.submit(() -> {
            try {
                gatewayLockManager.executeWithLock(gatewayCode, () -> {
                    try {
                        Thread.sleep(3000); // 持有锁3秒
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    return null;
                }, 5); // 5秒超时
            } finally {
                latch.countDown();
            }
        });
        
        // 稍等一下确保第一个线程先获取锁
        Thread.sleep(100);
        
        // 第二个线程尝试获取锁，但会超时
        executor.submit(() -> {
            try {
                assertThrows(RuntimeException.class, () -> {
                    gatewayLockManager.executeWithLock(gatewayCode, () -> {
                        return null;
                    }, 1); // 1秒超时，应该会超时
                });
            } finally {
                latch.countDown();
            }
        });
        
        assertTrue(latch.await(10, TimeUnit.SECONDS));
        executor.shutdown();
    }
    
    /**
     * 测试锁状态查询功能
     */
    @Test
    void testLockStatusQuery() throws InterruptedException {
        String gatewayCode = "GATEWAY_001";
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch finishLatch = new CountDownLatch(1);
        ExecutorService executor = Executors.newSingleThreadExecutor();
        
        // 启动一个线程持有锁
        executor.submit(() -> {
            gatewayLockManager.executeWithLock(gatewayCode, () -> {
                startLatch.countDown();
                try {
                    finishLatch.await();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                return null;
            });
        });
        
        // 等待线程获取锁
        assertTrue(startLatch.await(5, TimeUnit.SECONDS));
        
        // 检查锁状态
        assertTrue(gatewayLockManager.isLocked(gatewayCode));
        assertEquals(1, gatewayLockManager.getLockCount());
        
        // 释放锁
        finishLatch.countDown();
        
        // 等待一下让锁被释放
        Thread.sleep(100);
        
        executor.shutdown();
    }
    
    /**
     * 测试统计信息功能
     */
    @Test
    void testStatistics() {
        String gatewayCode1 = "GATEWAY_001";
        String gatewayCode2 = "GATEWAY_002";

        // 执行一些操作
        gatewayLockManager.executeWithLock(gatewayCode1, () -> "result1");
        gatewayLockManager.executeWithLock(gatewayCode2, () -> "result2");

        String stats = gatewayLockManager.getStatistics();
        assertNotNull(stats);
        assertTrue(stats.contains("网关锁统计信息"));

        log.info("统计信息:\n{}", stats);
    }

    /**
     * 测试异常处理
     */
    @Test
    void testExceptionHandling() {
        String gatewayCode = "GATEWAY_001";

        // 测试空网关编号
        assertThrows(IllegalArgumentException.class, () -> {
            gatewayLockManager.executeWithLock(null, () -> "result");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            gatewayLockManager.executeWithLock("", () -> "result");
        });

        // 测试操作中抛出异常
        assertThrows(RuntimeException.class, () -> {
            gatewayLockManager.executeWithLock(gatewayCode, () -> {
                throw new RuntimeException("测试异常");
            });
        });
    }
}
