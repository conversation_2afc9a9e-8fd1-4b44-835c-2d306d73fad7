package com.siact.control.mqtt;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.siact.control.service.PointLevelFutureManager;

import com.siact.control.utils.DefaultFuture;
import com.siact.control.utils.PointStatusHandler;
import com.siact.control.utils.TaskUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MqttResHandler {

    @Autowired
    private PointLevelFutureManager pointLevelFutureManager;

    /**
     * 处理MQTT响应消息 - 支持点位级别分散返回
     * @param msg 响应消息
     */
    public void deal(Message msg) {
        //根据messageId判断是否是本机发送的消息
        Long msgId = msg.getMessageId();
        String gatewayCode = msg.getGatewayCode();

        // 直接处理点位级别的响应

        try {
            JSONObject responseObj = JSONObject.parseObject(msg.getPlayLoad());
            handlePointLevelResponse(msgId, gatewayCode, responseObj);
        } catch (Exception e) {
            log.error("解析响应内容失败 - 网关: {}, 消息ID: {}, 错误: {}", gatewayCode, msgId, e.getMessage());
        }
    }


    /**
     * 处理MQTT响应消息
     * @param msg 响应消息
     */
    public void dealRead(Message msg) {
        //根据messageId判断是否是本机发送的消息
        Long msgId = msg.getMessageId();
        String gatewayCode = msg.getGatewayCode();

        // 检查是否存在匹配的请求
        boolean contains = DefaultFuture.contains(gatewayCode, msgId);
        log.info("检查请求匹配 - 网关: {}, 消息ID: {}, 是否匹配: {}", gatewayCode, msgId, contains);

        if (contains) {
            log.info("找到匹配请求，处理响应 - 网关: {}, 消息ID: {}", gatewayCode, msgId);
            DefaultFuture.received(msg);
        } else {
            log.warn("未找到匹配请求 - 网关: {}, 消息ID: {}, 可能原因: 1.请求已超时 2.网关编号不匹配 3.消息ID不匹配",
                    gatewayCode, msgId);
        }
    }

    /**
     * 处理点位级别的响应
     */
    private void handlePointLevelResponse(Long commandId, String gatewayCode, JSONObject responseObj) {
        try {
            String cmdstate = responseObj.getString("cmdstate");
            if (StringUtils.isBlank(cmdstate)) {
                log.warn("响应中没有cmdstate字段 - 命令ID: {}, 网关: {}", commandId, gatewayCode);
                return;
            }

            // 解析点位状态：格式为 "点位1:状态1/点位2:状态2/..."
            String[] pointStatuses = cmdstate.split("/");
            for (String pointStatus : pointStatuses) {
                String[] parts = pointStatus.split(":");
                if (parts.length >= 2) {
                    String itemId = parts[0];
                    String originalStatus = parts[1];

                    // 获取点位对应的属性编码
                    String propCode = TaskUtil.iotPropMap.get(gatewayCode + "_" + itemId);
                    if (StringUtils.isNotBlank(propCode)) {
                        // 如果是NOTAG状态，增加统计计数
                        if (PointStatusHandler.isSpecialStatus(originalStatus)) {
                            pointLevelFutureManager.handleNotagResponse(commandId);
                        }

                        // 处理NOTAG状态转换（只记录日志，不添加标记）
                        String processedStatus = PointStatusHandler.processStatus(originalStatus);
                        if (PointStatusHandler.isSpecialStatus(originalStatus)) {
                            log.error("网关中点位不存在 - 命令ID: {}, 网关: {}, 点位: {}, 将按照false处理",
                                    commandId, gatewayCode, itemId);
                            log.warn("点位不存在异常 - 命令ID: {}, 网关: {}, 点位: {}, 原始状态: {}",
                                    commandId, gatewayCode, itemId, originalStatus);
                        }

                        // 创建点位结果对象（保持字符串格式）
                        JSONObject itemResult = new JSONObject();
                        itemResult.put("propCode", propCode);
                        itemResult.put("result", processedStatus.toLowerCase());

                        // 通知点位级别管理器
                        pointLevelFutureManager.handleItemResponse(commandId, gatewayCode, itemId, itemResult);

                        // 清理已完成的点位映射
                        TaskUtil.iotPropMap.remove(gatewayCode + "_" + itemId);
                    } else {
                        log.warn("未找到点位属性映射 - 网关: {}, 点位: {}", gatewayCode, itemId);
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理点位级别响应异常 - 命令ID: {}, 网关: {}, 错误: {}",
                    commandId, gatewayCode, e.getMessage(), e);
        }
    }
}

