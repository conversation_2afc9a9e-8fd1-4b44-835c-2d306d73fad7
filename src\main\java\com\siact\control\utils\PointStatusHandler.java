package com.siact.control.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

/**
 * 点位状态处理工具类
 * 负责处理各种特殊的点位状态，如NOTAG等
 * 
 * <AUTHOR>
 * @create 2023/12/15
 */
@Slf4j
public class PointStatusHandler {
    
    /**
     * NOTAG状态常量
     */
    public static final String NOTAG_STATUS = "NOTAG";
    
    /**
     * 处理点位状态，包括特殊状态的转换和日志记录
     *
     * @param commandId 命令ID
     * @param gatewayCode 网关编号
     * @param itemId 点位ID
     * @param originalStatus 原始状态
     * @param propCode 属性编码
     * @return 处理后的结果对象
     */
    public static JSONObject handlePointStatus(Long commandId, String gatewayCode,
                                             String itemId, String originalStatus, String propCode) {

        JSONObject result = new JSONObject();
        result.put("propCode", propCode);
        result.put("gatewayCode", gatewayCode);
        result.put("itemId", itemId);

        String processedStatus = processStatus(originalStatus);
        result.put("result", processedStatus.toLowerCase());

        // 处理特殊状态（只记录日志，不添加标记）
        if (isSpecialStatus(originalStatus)) {
            handleSpecialStatusLogging(commandId, gatewayCode, itemId, originalStatus);
        }

        return result;
    }
    
    /**
     * 处理状态值，将特殊状态转换为标准状态
     * 
     * @param originalStatus 原始状态
     * @return 处理后的状态
     */
    public static String processStatus(String originalStatus) {
        if (NOTAG_STATUS.equalsIgnoreCase(originalStatus)) {
            return "false"; // NOTAG按照false处理
        }
        return originalStatus;
    }
    
    /**
     * 判断是否为特殊状态
     * 
     * @param status 状态值
     * @return 是否为特殊状态
     */
    public static boolean isSpecialStatus(String status) {
        return NOTAG_STATUS.equalsIgnoreCase(status);
    }
    
    /**
     * 处理特殊状态的日志记录
     *
     * @param commandId 命令ID
     * @param gatewayCode 网关编号
     * @param itemId 点位ID
     * @param originalStatus 原始状态
     */
    private static void handleSpecialStatusLogging(Long commandId, String gatewayCode,
                                                  String itemId, String originalStatus) {

        if (NOTAG_STATUS.equalsIgnoreCase(originalStatus)) {
            handleNotagStatusLogging(commandId, gatewayCode, itemId, originalStatus);
        }
        // 未来可以在这里添加其他特殊状态的处理
    }

    /**
     * 处理NOTAG状态的日志记录
     *
     * @param commandId 命令ID
     * @param gatewayCode 网关编号
     * @param itemId 点位ID
     * @param originalStatus 原始状态
     */
    private static void handleNotagStatusLogging(Long commandId, String gatewayCode,
                                               String itemId, String originalStatus) {

        // 记录错误日志
        log.error("网关中点位不存在 - 命令ID: {}, 网关: {}, 点位: {}, 将按照false处理",
                commandId, gatewayCode, itemId);

        // 记录警告日志（用于监控和统计）
        log.warn("点位不存在异常 - 命令ID: {}, 网关: {}, 点位: {}, 原始状态: {}",
                commandId, gatewayCode, itemId, originalStatus);
    }
    
    /**
     * 获取状态描述
     * 
     * @param status 状态值
     * @return 状态描述
     */
    public static String getStatusDescription(String status) {
        if (NOTAG_STATUS.equalsIgnoreCase(status)) {
            return "点位在网关中不存在";
        }
        if ("true".equalsIgnoreCase(status)) {
            return "控制成功";
        }
        if ("false".equalsIgnoreCase(status)) {
            return "控制失败";
        }
        return "未知状态: " + status;
    }
    
    /**
     * 检查结果是否包含异常
     *
     * @param result 结果对象
     * @return 是否包含异常
     */
    public static boolean hasError(JSONObject result) {
        return result.getBooleanValue("timeout") ||
               result.containsKey("errorType");
    }

    /**
     * 获取错误信息
     *
     * @param result 结果对象
     * @return 错误信息
     */
    public static String getErrorMessage(JSONObject result) {
        if (result.getBooleanValue("timeout")) {
            return "控制超时";
        }
        if (result.containsKey("errorMessage")) {
            return result.getString("errorMessage");
        }
        return null;
    }
    
    /**
     * 统计结果中的异常情况
     * 注意：由于不再在结果中添加notag标记，NOTAG统计需要在处理过程中单独记录
     *
     * @param results 结果数组
     * @return 统计信息
     */
    public static JSONObject getStatistics(com.alibaba.fastjson.JSONArray results) {
        JSONObject stats = new JSONObject();
        int total = results.size();
        int success = 0;
        int failed = 0;
        int timeout = 0;

        for (int i = 0; i < results.size(); i++) {
            JSONObject result = results.getJSONObject(i);

            if (result.getBooleanValue("timeout")) {
                timeout++;
            } else if ("true".equalsIgnoreCase(result.getString("result"))) {
                success++;
            } else {
                failed++;
            }
        }

        stats.put("total", total);
        stats.put("success", success);
        stats.put("failed", failed);
        stats.put("timeout", timeout);
        stats.put("successRate", total > 0 ? (double) success / total : 0.0);

        return stats;
    }
}
