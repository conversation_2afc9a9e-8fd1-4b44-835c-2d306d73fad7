package com.siact.control.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.siact.control.utils.PointStatusHandler;
import com.siact.control.utils.TaskUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


/**
 * 点位级别的Future管理器
 * 负责管理分散返回的点位控制结果
 * 
 * <AUTHOR>
 * @create 2023/12/15
 */
@Slf4j
@Service
public class PointLevelFutureManager {
    
    // 存储命令级别的完成度跟踪信息
    private static final Map<Long, CommandCompletionTracker> commandTrackers = new ConcurrentHashMap<>();
    

    

    
    /**
     * 命令完成度跟踪器
     */
    public static class CommandCompletionTracker {
        private final Long commandId;
        private final Map<String, Set<String>> gatewayExpectedItems; // 网关 -> 期望的点位集合
        private final Map<String, Set<String>> gatewayCompletedItems; // 网关 -> 已完成的点位集合
        private final JSONArray results; // 收集的结果
        private final long startTime;
        private volatile boolean completed = false;
        private volatile boolean timedOut = false;
        private volatile int notagCount = 0; // NOTAG数量统计
        
        public CommandCompletionTracker(Long commandId, Map<String, List<String>> gatewayItemsMap) {
            this.commandId = commandId;
            this.gatewayExpectedItems = new ConcurrentHashMap<>();
            this.gatewayCompletedItems = new ConcurrentHashMap<>();
            this.results = new JSONArray();
            this.startTime = System.currentTimeMillis();
            
            // 初始化期望的点位
            for (Map.Entry<String, List<String>> entry : gatewayItemsMap.entrySet()) {
                String gateway = entry.getKey();
                Set<String> expectedItems = new HashSet<>(entry.getValue());
                gatewayExpectedItems.put(gateway, expectedItems);
                gatewayCompletedItems.put(gateway, new HashSet<>());
            }
            
            log.info("创建命令完成度跟踪器 - 命令ID: {}, 网关数: {}, 总点位数: {}", 
                    commandId, gatewayExpectedItems.size(), getTotalExpectedCount());
        }
        
        /**
         * 添加完成的点位
         */
        public synchronized boolean addCompletedItem(String gateway, String itemId, JSONObject result) {
            if (completed || timedOut) {
                return false;
            }

            Set<String> expectedItems = gatewayExpectedItems.get(gateway);
            if (expectedItems == null || !expectedItems.contains(itemId)) {
                log.warn("收到未期望的点位响应 - 网关: {}, 点位: {}", gateway, itemId);
                return false;
            }

            Set<String> completedItems = gatewayCompletedItems.get(gateway);
            if (completedItems.add(itemId)) {
                results.add(result);
                log.info("点位完成 - 命令ID: {}, 网关: {}, 点位: {}, 进度: {}/{}",
                        commandId, gateway, itemId, getTotalCompletedCount(), getTotalExpectedCount());

                // 检查是否全部完成
                if (isAllCompleted()) {
                    completed = true;
                    log.info("命令全部完成 - 命令ID: {}, 耗时: {}ms",
                            commandId, System.currentTimeMillis() - startTime);
                    return true;
                }
            }
            return false;
        }

        /**
         * 增加NOTAG计数
         */
        public synchronized void incrementNotagCount() {
            notagCount++;
        }

        /**
         * 获取NOTAG数量
         */
        public int getNotagCount() {
            return notagCount;
        }
        
        /**
         * 标记网关超时
         */
        public synchronized void markGatewayTimeout(String gateway) {
            if (completed) {
                return;
            }
            
            Set<String> expectedItems = gatewayExpectedItems.get(gateway);
            Set<String> completedItems = gatewayCompletedItems.get(gateway);
            
            if (expectedItems != null && completedItems != null) {
                Set<String> timeoutItems = new HashSet<>(expectedItems);
                timeoutItems.removeAll(completedItems);
                
                // 为超时的点位添加超时结果（返回"false"字符串）
                for (String itemId : timeoutItems) {
                    JSONObject timeoutResult = new JSONObject();
                    timeoutResult.put("propCode", TaskUtil.iotPropMap.get(gateway + "_" + itemId));
                    timeoutResult.put("result", "false");  // 超时返回"false"字符串
                    results.add(timeoutResult);
                }
                
                log.warn("网关超时 - 命令ID: {}, 网关: {}, 超时点位数: {}", 
                        commandId, gateway, timeoutItems.size());
            }
            
            timedOut = true;
        }
        
        /**
         * 检查是否全部完成
         */
        public boolean isAllCompleted() {
            for (Map.Entry<String, Set<String>> entry : gatewayExpectedItems.entrySet()) {
                String gateway = entry.getKey();
                Set<String> expected = entry.getValue();
                Set<String> completed = gatewayCompletedItems.get(gateway);
                
                if (completed == null || completed.size() < expected.size()) {
                    return false;
                }
            }
            return true;
        }
        
        /**
         * 获取总的期望点位数
         */
        public int getTotalExpectedCount() {
            return gatewayExpectedItems.values().stream()
                    .mapToInt(Set::size)
                    .sum();
        }
        
        /**
         * 获取总的已完成点位数
         */
        public int getTotalCompletedCount() {
            return gatewayCompletedItems.values().stream()
                    .mapToInt(Set::size)
                    .sum();
        }
        
        public JSONArray getResults() {
            return results;
        }
        
        public boolean isCompleted() {
            return completed;
        }
        
        public boolean isTimedOut() {
            return timedOut;
        }
        
        public long getStartTime() {
            return startTime;
        }
    }
    
    /**
     * 创建命令跟踪器
     */
    public void createCommandTracker(Long commandId, Map<String, List<String>> gatewayItemsMap) {
        CommandCompletionTracker tracker = new CommandCompletionTracker(commandId, gatewayItemsMap);
        commandTrackers.put(commandId, tracker);
    }

    /**
     * 移除命令跟踪器（用于清理失败的命令）
     */
    public void removeCommandTracker(Long commandId) {
        commandTrackers.remove(commandId);
    }

    /**
     * 处理点位响应
     */
    public boolean handleItemResponse(Long commandId, String gateway, String itemId, JSONObject result) {
        CommandCompletionTracker tracker = commandTrackers.get(commandId);
        if (tracker == null) {
            log.warn("未找到命令跟踪器 - 命令ID: {}", commandId);
            return false;
        }

        return tracker.addCompletedItem(gateway, itemId, result);
    }

    /**
     * 处理NOTAG响应（用于统计）
     */
    public void handleNotagResponse(Long commandId) {
        CommandCompletionTracker tracker = commandTrackers.get(commandId);
        if (tracker != null) {
            tracker.incrementNotagCount();
        }
    }
    
    /**
     * 处理网关超时
     */
    public void handleGatewayTimeout(Long commandId, String gateway) {
        CommandCompletionTracker tracker = commandTrackers.get(commandId);
        if (tracker != null) {
            tracker.markGatewayTimeout(gateway);
        }
    }
    
    /**
     * 等待命令完成或超时
     */
    public JSONArray waitForCompletion(Long commandId, int timeoutSeconds) {
        CommandCompletionTracker tracker = commandTrackers.get(commandId);
        if (tracker == null) {
            log.error("未找到命令跟踪器 - 命令ID: {}", commandId);
            return null;
        }
        
        long startTime = System.currentTimeMillis();
        long timeoutMillis = timeoutSeconds * 1000L;
        
        try {
            while (!tracker.isCompleted() && !tracker.isTimedOut()) {
                long elapsed = System.currentTimeMillis() - startTime;
                if (elapsed >= timeoutMillis) {
                    log.warn("命令等待超时 - 命令ID: {}, 超时时间: {}秒, 完成进度: {}/{}", 
                            commandId, timeoutSeconds, tracker.getTotalCompletedCount(), tracker.getTotalExpectedCount());
                    
                    // 标记所有未完成的网关为超时
                    for (String gateway : tracker.gatewayExpectedItems.keySet()) {
                        Set<String> completed = tracker.gatewayCompletedItems.get(gateway);
                        Set<String> expected = tracker.gatewayExpectedItems.get(gateway);
                        if (completed == null || completed.size() < expected.size()) {
                            tracker.markGatewayTimeout(gateway);
                        }
                    }
                    break;
                }
                
                Thread.sleep(50); // 50ms检查一次
            }
            
            JSONArray results = tracker.getResults();

            // 输出统计信息
            if (results != null && results.size() > 0) {
                JSONObject stats = PointStatusHandler.getStatistics(results);
                int notagCount = tracker.getNotagCount();
                log.info("命令执行统计 - 命令ID: {}, 总数: {}, 成功: {}, 失败: {}, NOTAG: {}, 超时: {}, 成功率: {:.2f}%",
                        commandId,
                        stats.getInteger("total"),
                        stats.getInteger("success"),
                        stats.getInteger("failed"),
                        notagCount,
                        stats.getInteger("timeout"),
                        stats.getDoubleValue("successRate") * 100);
            }

            return results;

        } catch (InterruptedException e) {
            log.error("等待命令完成被中断 - 命令ID: {}", commandId, e);
            Thread.currentThread().interrupt();
            return null;
        } finally {
            // 清理跟踪器
            commandTrackers.remove(commandId);
        }
    }
    
    /**
     * 获取命令完成进度
     */
    public String getCompletionProgress(Long commandId) {
        CommandCompletionTracker tracker = commandTrackers.get(commandId);
        if (tracker == null) {
            return "未找到命令跟踪器";
        }
        
        return String.format("%d/%d", tracker.getTotalCompletedCount(), tracker.getTotalExpectedCount());
    }
    
    /**
     * 清理过期的跟踪器
     */
    public void cleanupExpiredTrackers(int maxAgeMinutes) {
        long cutoffTime = System.currentTimeMillis() - (maxAgeMinutes * 60 * 1000L);
        
        commandTrackers.entrySet().removeIf(entry -> {
            CommandCompletionTracker tracker = entry.getValue();
            if (tracker.getStartTime() < cutoffTime) {
                log.info("清理过期的命令跟踪器 - 命令ID: {}", entry.getKey());
                return true;
            }
            return false;
        });
    }
}
