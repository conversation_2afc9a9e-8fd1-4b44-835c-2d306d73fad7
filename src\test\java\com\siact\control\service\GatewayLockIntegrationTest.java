package com.siact.control.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 网关锁集成测试
 * 验证修复后的解决方案
 * 
 * <AUTHOR>
 * @create 2024/8/11
 */
@Slf4j
@SpringBootTest
public class GatewayLockIntegrationTest {
    
    @Autowired(required = false)
    private GatewayLockManager gatewayLockManager;
    
    /**
     * 测试网关锁的基本功能
     */
    @Test
    void testGatewayLockBasicFunctionality() {
        if (gatewayLockManager == null) {
            log.warn("GatewayLockManager未注入，跳过测试");
            return;
        }
        
        String gatewayCode = "TEST_GATEWAY_001";
        
        // 测试基本的锁功能
        String result = gatewayLockManager.executeWithLock(gatewayCode, () -> {
            log.info("在网关锁保护下执行操作 - 网关: {}", gatewayCode);
            return "操作成功";
        });
        
        assertEquals("操作成功", result);
        log.info("网关锁基本功能测试通过");
    }
    
    /**
     * 测试同一网关的串行执行
     */
    @Test
    void testSameGatewaySerialExecution() throws InterruptedException {
        if (gatewayLockManager == null) {
            log.warn("GatewayLockManager未注入，跳过测试");
            return;
        }

        String gatewayCode = "TEST_GATEWAY_SERIAL";
        ExecutorService executor = Executors.newFixedThreadPool(2);

        long startTime = System.currentTimeMillis();

        // 提交2个任务到同一网关
        CompletableFuture<?>[] futures = new CompletableFuture[2];
        for (int i = 0; i < 2; i++) {
            final int taskId = i + 1;
            futures[i] = CompletableFuture.runAsync(() -> {
                gatewayLockManager.executeWithLock(gatewayCode, () -> {
                    log.info("任务 {} 执行 - 网关: {}", taskId, gatewayCode);
                    try {
                        Thread.sleep(300); // 模拟处理时间
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    return null;
                });
            }, executor);
        }

        // 等待所有任务完成
        CompletableFuture.allOf(futures).join();

        long totalTime = System.currentTimeMillis() - startTime;
        log.info("串行执行测试完成，总耗时: {}ms", totalTime);

        // 串行执行应该至少需要 2 * 300 = 600ms
        assertTrue(totalTime >= 600, "串行执行时间应该至少600ms");

        executor.shutdown();
        assertTrue(executor.awaitTermination(5, TimeUnit.SECONDS));
    }
    
    /**
     * 测试网关锁统计功能
     */
    @Test
    void testGatewayLockStatistics() {
        if (gatewayLockManager == null) {
            log.warn("GatewayLockManager未注入，跳过测试");
            return;
        }

        String gatewayCode = "TEST_GATEWAY_STATS";

        // 执行操作
        gatewayLockManager.executeWithLock(gatewayCode, () -> {
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return "测试";
        });

        // 获取统计信息
        String stats = gatewayLockManager.getStatistics();
        assertNotNull(stats);
        assertTrue(stats.contains("网关锁统计"));

        log.info("统计功能测试通过");
    }
}
