package com.siact.control.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 网关锁集成测试
 * 验证修复后的解决方案
 * 
 * <AUTHOR>
 * @create 2024/8/11
 */
@Slf4j
@SpringBootTest
public class GatewayLockIntegrationTest {
    
    @Autowired(required = false)
    private GatewayLockManager gatewayLockManager;
    
    /**
     * 测试网关锁的基本功能
     */
    @Test
    void testGatewayLockBasicFunctionality() {
        if (gatewayLockManager == null) {
            log.warn("GatewayLockManager未注入，跳过测试");
            return;
        }
        
        String gatewayCode = "TEST_GATEWAY_001";
        
        // 测试基本的锁功能
        String result = gatewayLockManager.executeWithLock(gatewayCode, () -> {
            log.info("在网关锁保护下执行操作 - 网关: {}", gatewayCode);
            return "操作成功";
        });
        
        assertEquals("操作成功", result);
        log.info("网关锁基本功能测试通过");
    }
    
    /**
     * 测试同一网关的串行执行
     */
    @Test
    void testSameGatewaySerialExecution() throws InterruptedException {
        if (gatewayLockManager == null) {
            log.warn("GatewayLockManager未注入，跳过测试");
            return;
        }
        
        String gatewayCode = "TEST_GATEWAY_SERIAL";
        ExecutorService executor = Executors.newFixedThreadPool(3);
        
        long startTime = System.currentTimeMillis();
        
        // 提交3个任务到同一网关
        CompletableFuture<?>[] futures = new CompletableFuture[3];
        for (int i = 0; i < 3; i++) {
            final int taskId = i + 1;
            futures[i] = CompletableFuture.runAsync(() -> {
                gatewayLockManager.executeWithLock(gatewayCode, () -> {
                    log.info("任务 {} 开始执行 - 网关: {}", taskId, gatewayCode);
                    try {
                        Thread.sleep(200); // 模拟处理时间
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    log.info("任务 {} 执行完成 - 网关: {}", taskId, gatewayCode);
                    return null;
                });
            }, executor);
        }
        
        // 等待所有任务完成
        CompletableFuture.allOf(futures).join();
        
        long totalTime = System.currentTimeMillis() - startTime;
        log.info("同一网关串行执行测试完成，总耗时: {}ms", totalTime);
        
        // 串行执行应该至少需要 3 * 200 = 600ms
        assertTrue(totalTime >= 600, "串行执行时间应该至少600ms，实际: " + totalTime + "ms");
        
        executor.shutdown();
        assertTrue(executor.awaitTermination(5, TimeUnit.SECONDS));
    }
    
    /**
     * 测试不同网关的并行执行
     */
    @Test
    void testDifferentGatewayParallelExecution() throws InterruptedException {
        if (gatewayLockManager == null) {
            log.warn("GatewayLockManager未注入，跳过测试");
            return;
        }
        
        String[] gateways = {"TEST_GATEWAY_A", "TEST_GATEWAY_B", "TEST_GATEWAY_C"};
        ExecutorService executor = Executors.newFixedThreadPool(3);
        
        long startTime = System.currentTimeMillis();
        
        // 为每个网关提交任务
        CompletableFuture<?>[] futures = new CompletableFuture[gateways.length];
        for (int i = 0; i < gateways.length; i++) {
            final String gatewayCode = gateways[i];
            futures[i] = CompletableFuture.runAsync(() -> {
                gatewayLockManager.executeWithLock(gatewayCode, () -> {
                    log.info("网关 {} 开始处理", gatewayCode);
                    try {
                        Thread.sleep(300); // 模拟处理时间
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    log.info("网关 {} 处理完成", gatewayCode);
                    return null;
                });
            }, executor);
        }
        
        // 等待所有任务完成
        CompletableFuture.allOf(futures).join();
        
        long totalTime = System.currentTimeMillis() - startTime;
        log.info("不同网关并行执行测试完成，总耗时: {}ms", totalTime);
        
        // 并行执行应该接近单个任务的时间（300ms），允许一些误差
        assertTrue(totalTime < 600, "并行执行时间应该小于600ms，实际: " + totalTime + "ms");
        
        executor.shutdown();
        assertTrue(executor.awaitTermination(5, TimeUnit.SECONDS));
    }
    
    /**
     * 测试网关锁统计功能
     */
    @Test
    void testGatewayLockStatistics() {
        if (gatewayLockManager == null) {
            log.warn("GatewayLockManager未注入，跳过测试");
            return;
        }
        
        String gatewayCode = "TEST_GATEWAY_STATS";
        
        // 执行一些操作
        gatewayLockManager.executeWithLock(gatewayCode, () -> {
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return "测试";
        });
        
        // 获取统计信息
        String stats = gatewayLockManager.getStatistics();
        assertNotNull(stats);
        assertTrue(stats.contains("网关锁统计信息"));
        
        log.info("网关锁统计信息:\n{}", stats);
    }
    
    /**
     * 测试网关锁的异常处理
     */
    @Test
    void testGatewayLockExceptionHandling() {
        if (gatewayLockManager == null) {
            log.warn("GatewayLockManager未注入，跳过测试");
            return;
        }
        
        String gatewayCode = "TEST_GATEWAY_EXCEPTION";
        
        // 测试空网关编号
        assertThrows(IllegalArgumentException.class, () -> {
            gatewayLockManager.executeWithLock(null, () -> "test");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            gatewayLockManager.executeWithLock("", () -> "test");
        });
        
        // 测试操作中抛出异常
        assertThrows(RuntimeException.class, () -> {
            gatewayLockManager.executeWithLock(gatewayCode, () -> {
                throw new RuntimeException("测试异常");
            });
        });
        
        log.info("网关锁异常处理测试通过");
    }
    
    /**
     * 模拟实际的数据查询场景
     */
    @Test
    void testRealDataQueryScenario() {
        if (gatewayLockManager == null) {
            log.warn("GatewayLockManager未注入，跳过测试");
            return;
        }
        
        // 模拟实际的数据点编码
        List<String> dataPoints = Arrays.asList(
            "PGY02013_SPD01002_ST00000000_U00000000_BJPD01JXDB1021_MPPT12001",
            "PGY02013_SPD01002_STCNZ01001_UCNDY1001_EQPD01CNSB1001_MPKGS2001"
        );
        
        String gatewayCode = "SiACT20250312510218";
        
        // 模拟数据查询过程
        List<String> result = gatewayLockManager.executeWithLock(gatewayCode, () -> {
            log.info("模拟查询网关 {} 的数据点: {}", gatewayCode, dataPoints);
            
            // 模拟MQTT通信延迟
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            log.info("网关 {} 数据查询完成", gatewayCode);
            return dataPoints; // 返回查询结果
        });
        
        assertNotNull(result);
        assertEquals(dataPoints.size(), result.size());
        
        log.info("实际数据查询场景测试通过");
    }
}
