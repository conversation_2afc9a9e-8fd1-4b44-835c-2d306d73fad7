package com.siact.control.service;

import com.siact.control.entity.DataCodeValDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 实时数据查询集成测试
 * 验证网关锁机制在实际业务场景中的效果
 * 
 * <AUTHOR>
 * @create 2024/8/11
 */
@Slf4j
@SpringBootTest
public class RealTimeDataQueryTest {
    
    @Autowired(required = false)
    private CommonService commonService;
    
    @Autowired(required = false)
    private GatewayLockManager gatewayLockManager;
    
    /**
     * 测试并发查询同一网关的不同点位
     * 验证请求是否串行执行，避免覆盖问题
     */
    @Test
    void testConcurrentQuerySameGateway() throws InterruptedException {
        // 如果服务未注入（可能在单元测试环境），跳过测试
        if (commonService == null || gatewayLockManager == null) {
            log.warn("服务未注入，跳过集成测试");
            return;
        }
        
        int threadCount = 5;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch finishLatch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        List<List<DataCodeValDTO>> results = new ArrayList<>();
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        
        // 模拟同时查询同一网关的不同点位
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    // 等待所有线程准备就绪
                    startLatch.await();
                    
                    // 构造查询请求（这里使用模拟数据，实际环境需要真实的dataCode）
                    List<String> dataCodeList = Arrays.asList(
                        "TEST_GATEWAY_001_POINT_" + threadId + "_01",
                        "TEST_GATEWAY_001_POINT_" + threadId + "_02"
                    );
                    
                    log.info("线程 {} 开始查询数据: {}", threadId, dataCodeList);
                    long startTime = System.currentTimeMillis();
                    
                    try {
                        List<DataCodeValDTO> result = commonService.getRealTimeData(dataCodeList);
                        long duration = System.currentTimeMillis() - startTime;
                        
                        synchronized (results) {
                            results.add(result);
                        }
                        
                        successCount.incrementAndGet();
                        log.info("线程 {} 查询成功，耗时: {}ms，结果数量: {}", 
                                threadId, duration, result != null ? result.size() : 0);
                        
                    } catch (Exception e) {
                        errorCount.incrementAndGet();
                        log.error("线程 {} 查询失败: {}", threadId, e.getMessage());
                    }
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("线程 {} 被中断", threadId);
                } finally {
                    finishLatch.countDown();
                }
            });
        }
        
        // 启动所有线程
        log.info("启动 {} 个并发查询线程", threadCount);
        startLatch.countDown();
        
        // 等待所有线程完成
        assertTrue(finishLatch.await(30, TimeUnit.SECONDS), "测试超时");
        
        // 验证结果
        log.info("测试完成 - 成功: {}, 失败: {}, 总结果数: {}", 
                successCount.get(), errorCount.get(), results.size());
        
        // 在测试环境中，由于没有真实的MQTT和设备，可能会有异常
        // 但重要的是验证锁机制工作正常，不会出现死锁或其他并发问题
        assertTrue(successCount.get() + errorCount.get() == threadCount, 
                  "所有线程都应该完成（成功或失败）");
        
        executor.shutdown();
    }
    
    /**
     * 测试并发查询不同网关
     * 验证不同网关的请求可以并行执行
     */
    @Test
    void testConcurrentQueryDifferentGateways() throws InterruptedException {
        if (commonService == null || gatewayLockManager == null) {
            log.warn("服务未注入，跳过集成测试");
            return;
        }
        
        int gatewayCount = 3;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch finishLatch = new CountDownLatch(gatewayCount);
        ExecutorService executor = Executors.newFixedThreadPool(gatewayCount);
        
        ConcurrentHashMap<String, Long> gatewayExecutionTimes = new ConcurrentHashMap<>();
        AtomicInteger successCount = new AtomicInteger(0);
        
        // 为每个网关启动查询线程
        for (int g = 0; g < gatewayCount; g++) {
            final String gatewayCode = "TEST_GATEWAY_" + String.format("%03d", g + 1);
            executor.submit(() -> {
                try {
                    startLatch.await();
                    
                    List<String> dataCodeList = Arrays.asList(
                        gatewayCode + "_POINT_01",
                        gatewayCode + "_POINT_02"
                    );
                    
                    long startTime = System.currentTimeMillis();
                    log.info("开始查询网关 {} 的数据", gatewayCode);
                    
                    try {
                        List<DataCodeValDTO> result = commonService.getRealTimeData(dataCodeList);
                        long duration = System.currentTimeMillis() - startTime;
                        
                        gatewayExecutionTimes.put(gatewayCode, duration);
                        successCount.incrementAndGet();
                        
                        log.info("网关 {} 查询完成，耗时: {}ms，结果数量: {}", 
                                gatewayCode, duration, result != null ? result.size() : 0);
                        
                    } catch (Exception e) {
                        log.error("网关 {} 查询失败: {}", gatewayCode, e.getMessage());
                    }
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    finishLatch.countDown();
                }
            });
        }
        
        log.info("启动 {} 个网关的并发查询", gatewayCount);
        long testStartTime = System.currentTimeMillis();
        startLatch.countDown();
        
        assertTrue(finishLatch.await(30, TimeUnit.SECONDS), "测试超时");
        long totalTestTime = System.currentTimeMillis() - testStartTime;
        
        log.info("所有网关查询完成，总耗时: {}ms", totalTestTime);
        gatewayExecutionTimes.forEach((gateway, time) -> {
            log.info("网关 {} 执行时间: {}ms", gateway, time);
        });
        
        // 验证不同网关的查询可以并行执行
        // 如果是串行执行，总时间应该接近各个网关时间的总和
        // 如果是并行执行，总时间应该接近最长的单个网关时间
        if (!gatewayExecutionTimes.isEmpty()) {
            long maxSingleGatewayTime = gatewayExecutionTimes.values().stream()
                    .mapToLong(Long::longValue)
                    .max()
                    .orElse(0L);
            
            // 并行执行的总时间应该不会比最长的单个网关时间多太多
            assertTrue(totalTestTime < maxSingleGatewayTime * 1.5, 
                      "并行执行时间应该接近最长的单个网关时间");
        }
        
        executor.shutdown();
    }
    
    /**
     * 测试网关锁的统计信息
     */
    @Test
    void testGatewayLockStatistics() {
        if (gatewayLockManager == null) {
            log.warn("GatewayLockManager未注入，跳过测试");
            return;
        }
        
        // 获取初始统计信息
        String initialStats = gatewayLockManager.getStatistics();
        log.info("初始统计信息:\n{}", initialStats);
        
        // 执行一些操作
        gatewayLockManager.executeWithLock("TEST_GATEWAY_001", () -> {
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return "test";
        });
        
        // 获取操作后的统计信息
        String finalStats = gatewayLockManager.getStatistics();
        log.info("操作后统计信息:\n{}", finalStats);
        
        assertNotNull(finalStats);
        assertTrue(finalStats.contains("网关锁统计信息"));
    }
    
    /**
     * 压力测试：大量并发请求
     */
    @Test
    void testHighConcurrencyLoad() throws InterruptedException {
        if (commonService == null || gatewayLockManager == null) {
            log.warn("服务未注入，跳过压力测试");
            return;
        }
        
        int totalRequests = 20;
        int gatewayCount = 4;
        CountDownLatch finishLatch = new CountDownLatch(totalRequests);
        ExecutorService executor = Executors.newFixedThreadPool(10);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        
        long startTime = System.currentTimeMillis();
        
        // 提交大量请求
        for (int i = 0; i < totalRequests; i++) {
            final int requestId = i;
            final String gatewayCode = "LOAD_TEST_GATEWAY_" + (i % gatewayCount + 1);
            
            executor.submit(() -> {
                try {
                    List<String> dataCodeList = Arrays.asList(
                        gatewayCode + "_POINT_" + requestId + "_01"
                    );
                    
                    List<DataCodeValDTO> result = commonService.getRealTimeData(dataCodeList);
                    successCount.incrementAndGet();
                    
                } catch (Exception e) {
                    errorCount.incrementAndGet();
                    log.debug("请求 {} 失败: {}", requestId, e.getMessage());
                } finally {
                    finishLatch.countDown();
                }
            });
        }
        
        // 等待所有请求完成
        assertTrue(finishLatch.await(60, TimeUnit.SECONDS), "压力测试超时");
        
        long duration = System.currentTimeMillis() - startTime;
        
        log.info("压力测试完成 - 总请求: {}, 成功: {}, 失败: {}, 耗时: {}ms, 平均TPS: {}", 
                totalRequests, successCount.get(), errorCount.get(), duration,
                totalRequests * 1000.0 / duration);
        
        // 验证所有请求都得到了处理
        assertEquals(totalRequests, successCount.get() + errorCount.get());
        
        // 输出最终的锁统计信息
        log.info("压力测试后的锁统计:\n{}", gatewayLockManager.getStatistics());
        
        executor.shutdown();
    }
}
