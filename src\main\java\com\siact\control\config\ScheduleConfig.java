package com.siact.control.config;

import com.siact.control.service.PointLevelFutureManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 定时任务配置
 * 
 * <AUTHOR>
 * @create 2023/12/15
 */
@Configuration
@EnableScheduling
@Slf4j
public class ScheduleConfig {

    @Autowired
    private PointLevelFutureManager pointLevelFutureManager;

    /**
     * 每5分钟清理一次过期的命令跟踪器
     * 清理超过10分钟的跟踪器
     */
    @Scheduled(fixedRate = 5 * 60 * 1000) // 5分钟
    public void cleanupExpiredTrackers() {
        try {
            pointLevelFutureManager.cleanupExpiredTrackers(10); // 清理10分钟前的
            log.debug("定时清理过期命令跟踪器完成");
        } catch (Exception e) {
            log.error("清理过期命令跟踪器异常", e);
        }
    }
}
