package com.siact.control.demo;

import com.siact.control.service.GatewayLockManager;
import com.siact.control.utils.CommandIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 网关锁演示程序
 * 展示网关锁机制如何解决并发访问问题
 * 
 * <AUTHOR>
 * @create 2024/8/11
 */
@Slf4j
@Component
public class GatewayLockDemo implements CommandLineRunner {
    
    @Autowired
    private GatewayLockManager gatewayLockManager;
    
    @Override
    public void run(String... args) throws Exception {
        // 只在特定条件下运行演示（避免在正常启动时执行）
        if (args.length > 0 && "demo".equals(args[0])) {
            log.info("=== 网关锁机制演示开始 ===");
            
            // 演示1：同一网关的串行执行
            demonstrateSerialExecution();
            
            Thread.sleep(2000);
            
            // 演示2：不同网关的并行执行
            demonstrateParallelExecution();
            
            Thread.sleep(2000);
            
            // 演示3：命令ID生成
            demonstrateCommandIdGeneration();
            
            log.info("=== 网关锁机制演示结束 ===");
        }
    }
    
    /**
     * 演示同一网关的串行执行
     */
    private void demonstrateSerialExecution() {
        log.info("\n--- 演示1：同一网关的串行执行 ---");
        
        String gatewayCode = "DEMO_GATEWAY_001";
        ExecutorService executor = Executors.newFixedThreadPool(3);
        
        // 同时提交3个任务到同一网关
        for (int i = 1; i <= 3; i++) {
            final int taskId = i;
            executor.submit(() -> {
                gatewayLockManager.executeWithLock(gatewayCode, () -> {
                    log.info("任务 {} 开始执行 - 网关: {}", taskId, gatewayCode);
                    
                    // 模拟实时数据查询处理时间
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    
                    log.info("任务 {} 执行完成 - 网关: {}", taskId, gatewayCode);
                    return "任务" + taskId + "结果";
                });
            });
        }
        
        executor.shutdown();
        try {
            executor.awaitTermination(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        log.info("同一网关串行执行演示完成");
    }
    
    /**
     * 演示不同网关的并行执行
     */
    private void demonstrateParallelExecution() {
        log.info("\n--- 演示2：不同网关的并行执行 ---");
        
        String[] gateways = {"DEMO_GATEWAY_A", "DEMO_GATEWAY_B", "DEMO_GATEWAY_C"};
        
        long startTime = System.currentTimeMillis();
        
        // 为每个网关创建并行任务
        CompletableFuture<?>[] futures = new CompletableFuture[gateways.length];
        
        for (int i = 0; i < gateways.length; i++) {
            final String gatewayCode = gateways[i];
            final int gatewayIndex = i + 1;
            
            futures[i] = CompletableFuture.runAsync(() -> {
                gatewayLockManager.executeWithLock(gatewayCode, () -> {
                    log.info("网关 {} 开始处理数据查询", gatewayCode);
                    
                    // 模拟不同网关的处理时间
                    try {
                        Thread.sleep(800 + gatewayIndex * 200);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    
                    log.info("网关 {} 数据查询完成", gatewayCode);
                    return "网关" + gatewayCode + "查询结果";
                });
            });
        }
        
        // 等待所有任务完成
        CompletableFuture.allOf(futures).join();
        
        long totalTime = System.currentTimeMillis() - startTime;
        log.info("不同网关并行执行演示完成，总耗时: {}ms", totalTime);
        log.info("如果是串行执行，预期耗时约: {}ms", 800 + 1000 + 1200);
    }
    
    /**
     * 演示命令ID生成
     */
    private void demonstrateCommandIdGeneration() {
        log.info("\n--- 演示3：命令ID生成机制 ---");
        
        // 生成控制命令ID
        log.info("控制命令ID生成:");
        for (int i = 0; i < 5; i++) {
            Long controlId = CommandIdGenerator.generateControlCommandId();
            log.info("  控制命令ID: {}", controlId);
        }
        
        // 生成读取命令ID
        log.info("读取命令ID生成:");
        for (int i = 0; i < 5; i++) {
            Long readId = CommandIdGenerator.generateReadCommandId();
            log.info("  读取命令ID: {}", readId);
        }
        
        // 验证ID类型判断
        Long controlId = CommandIdGenerator.generateControlCommandId();
        Long readId = CommandIdGenerator.generateReadCommandId();
        
        log.info("ID类型验证:");
        log.info("  {} 是控制命令ID: {}", controlId, CommandIdGenerator.isControlCommandId(controlId));
        log.info("  {} 是读取命令ID: {}", readId, CommandIdGenerator.isReadCommandId(readId));
        log.info("  {} 是控制命令ID: {}", readId, CommandIdGenerator.isControlCommandId(readId));
        log.info("  {} 是读取命令ID: {}", controlId, CommandIdGenerator.isReadCommandId(controlId));
    }
    
    /**
     * 模拟实时数据查询场景
     */
    public void simulateRealTimeDataQuery(String gatewayCode, String[] dataPoints) {
        log.info("模拟实时数据查询 - 网关: {}, 点位: {}", gatewayCode, String.join(",", dataPoints));
        
        gatewayLockManager.executeWithLock(gatewayCode, () -> {
            // 生成读取命令ID
            Long commandId = CommandIdGenerator.generateReadCommandId();
            log.info("生成读取命令ID: {}", commandId);
            
            // 模拟MQTT命令发送
            log.info("发送MQTT读取命令到网关: {}", gatewayCode);
            
            // 模拟网络延迟和设备响应时间
            try {
                Thread.sleep(500 + (int)(Math.random() * 1000));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            // 模拟接收响应
            log.info("接收到网关 {} 的响应，命令ID: {}", gatewayCode, commandId);
            
            // 返回模拟数据
            return "网关" + gatewayCode + "的实时数据";
        });
    }
    
    /**
     * 获取网关锁统计信息
     */
    public void printLockStatistics() {
        log.info("\n--- 网关锁统计信息 ---");
        String stats = gatewayLockManager.getStatistics();
        log.info(stats);
    }
}
