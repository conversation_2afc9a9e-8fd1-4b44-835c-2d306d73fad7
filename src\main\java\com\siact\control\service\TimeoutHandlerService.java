package com.siact.control.service;

import com.alibaba.fastjson.JSONObject;
import com.siact.control.exception.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 超时处理服务
 * 负责处理各种超时情况，记录超时日志
 * 注意：不进行任何重试操作
 * 
 * <AUTHOR>
 * @create 2023/4/4
 */
@Slf4j
@Service
public class TimeoutHandlerService {
    
    @Autowired
    private CommandDetailService commandDetailService;
    
    /**
     * 处理控制命令超时
     * 不进行重试，只记录超时状态和日志
     * 
     * @param commandId 命令ID
     * @param gatewayCode 网关编号
     */
    public void handleControlTimeout(Long commandId, String gatewayCode) {
        log.error("控制命令超时 - 命令ID: {}, 网关: {}", commandId, gatewayCode);
        
        try {
            // 更新命令状态为超时失败
            updateCommandTimeoutStatus(commandId, gatewayCode);
            
        } catch (Exception e) {
            log.error("处理控制命令超时异常 - 命令ID: {}, 网关: {}, 错误: {}", 
                    commandId, gatewayCode, e.getMessage(), e);
        }
    }
    
    /**
     * 处理读取命令超时
     * 不进行重试，只记录超时日志
     * 
     * @param gatewayCode 网关编号
     * @param requestInfo 请求信息（可为null）
     */
    public void handleReadTimeout(String gatewayCode, String requestInfo) {
        log.error("读取命令超时 - 网关: {}, 请求信息: {}", gatewayCode, requestInfo);
    }
    
    /**
     * 更新命令超时状态到数据库
     */
    private void updateCommandTimeoutStatus(Long commandId, String gatewayCode) {
        try {
            // 构建超时结果
            JSONObject timeoutResult = new JSONObject();
            timeoutResult.put("code", ErrorCode.REQUEST_TIMEOUT.getCode());
            timeoutResult.put("msg", "控制命令超时");
            timeoutResult.put("data", null);
            
            // 更新命令详情状态为失败（2表示失败状态）
            boolean updated = commandDetailService.updateByCommandId(
                    commandId, 
                    timeoutResult.toJSONString(), 
                    2
            );
            
            if (updated) {
                log.info("命令超时状态更新成功 - 命令ID: {}, 网关: {}", commandId, gatewayCode);
            } else {
                log.warn("命令超时状态更新失败 - 命令ID: {}, 网关: {}", commandId, gatewayCode);
            }
            
        } catch (Exception e) {
            log.error("更新命令超时状态异常 - 命令ID: {}, 网关: {}, 错误: {}", 
                    commandId, gatewayCode, e.getMessage(), e);
        }
    }
}