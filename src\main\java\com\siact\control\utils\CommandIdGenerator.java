package com.siact.control.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 命令ID生成器
 * 为不同类型的命令生成唯一的ID，避免ID冲突
 * 
 * <AUTHOR>
 * @create 2024/8/11
 */
@Slf4j
public class CommandIdGenerator {
    
    /**
     * 控制命令ID生成器，从1开始
     */
    private static final AtomicLong CONTROL_COMMAND_ID = new AtomicLong(1);
    
    /**
     * 读取命令ID生成器，从100000开始，避免与控制命令ID冲突
     */
    private static final AtomicLong READ_COMMAND_ID = new AtomicLong(100000);
    
    /**
     * 读取命令ID的最大值，超过后重置
     */
    private static final long READ_COMMAND_MAX_ID = 999999;
    
    /**
     * 控制命令ID的最大值，超过后重置
     */
    private static final long CONTROL_COMMAND_MAX_ID = 99999;
    
    /**
     * 生成控制命令ID
     * 
     * @return 唯一的控制命令ID
     */
    public static Long generateControlCommandId() {
        long id = CONTROL_COMMAND_ID.getAndIncrement();
        
        // 如果超过最大值，重置为1
        if (id > CONTROL_COMMAND_MAX_ID) {
            CONTROL_COMMAND_ID.set(1);
            id = CONTROL_COMMAND_ID.getAndIncrement();
            log.debug("控制命令ID重置，新ID: {}", id);
        }
        
        return id;
    }
    
    /**
     * 生成读取命令ID
     * 
     * @return 唯一的读取命令ID
     */
    public static Long generateReadCommandId() {
        long id = READ_COMMAND_ID.getAndIncrement();
        
        // 如果超过最大值，重置为100000
        if (id > READ_COMMAND_MAX_ID) {
            READ_COMMAND_ID.set(100000);
            id = READ_COMMAND_ID.getAndIncrement();
            log.debug("读取命令ID重置，新ID: {}", id);
        }
        
        return id;
    }
    
    /**
     * 获取当前控制命令ID计数器的值（不递增）
     * 
     * @return 当前控制命令ID
     */
    public static Long getCurrentControlCommandId() {
        return CONTROL_COMMAND_ID.get();
    }
    
    /**
     * 获取当前读取命令ID计数器的值（不递增）
     * 
     * @return 当前读取命令ID
     */
    public static Long getCurrentReadCommandId() {
        return READ_COMMAND_ID.get();
    }
    
    /**
     * 重置所有命令ID生成器（仅用于测试）
     */
    public static void resetAll() {
        CONTROL_COMMAND_ID.set(1);
        READ_COMMAND_ID.set(100000);
        log.warn("所有命令ID生成器已重置");
    }
    
    /**
     * 检查给定ID是否为读取命令ID
     * 
     * @param commandId 命令ID
     * @return 是否为读取命令ID
     */
    public static boolean isReadCommandId(Long commandId) {
        return commandId != null && commandId >= 100000 && commandId <= READ_COMMAND_MAX_ID;
    }
    
    /**
     * 检查给定ID是否为控制命令ID
     * 
     * @param commandId 命令ID
     * @return 是否为控制命令ID
     */
    public static boolean isControlCommandId(Long commandId) {
        return commandId != null && commandId >= 1 && commandId <= CONTROL_COMMAND_MAX_ID;
    }
}
