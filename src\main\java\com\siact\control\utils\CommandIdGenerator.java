package com.siact.control.utils;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 命令ID生成器
 * 为控制命令生成唯一的ID
 *
 * <AUTHOR>
 * @create 2024/8/11
 */
public class CommandIdGenerator {

    /**
     * 控制命令ID生成器，从1开始
     */
    private static final AtomicLong CONTROL_COMMAND_ID = new AtomicLong(1);

    /**
     * 控制命令ID的最大值，超过后重置
     */
    private static final long CONTROL_COMMAND_MAX_ID = 99999;

    /**
     * 生成控制命令ID
     */
    public static Long generateControlCommandId() {
        long id = CONTROL_COMMAND_ID.getAndIncrement();

        // 如果超过最大值，重置为1
        if (id > CONTROL_COMMAND_MAX_ID) {
            CONTROL_COMMAND_ID.set(1);
            id = CONTROL_COMMAND_ID.getAndIncrement();
        }

        return id;
    }
}
