# 网关锁解决方案

## 问题背景

在MEOS控制接口系统中，数据实时查询接口存在以下问题：

1. **固定命令ID冲突**：所有实时查询请求都使用固定的命令ID `99999`
2. **同网关并发覆盖**：当同时请求同一网关的不同点位时，会出现响应覆盖问题
3. **响应匹配错误**：由于命令ID相同，无法正确匹配请求和响应

## 解决方案设计

### 核心思想

- **不同网关并行**：不同网关的请求可以同时执行，提高系统吞吐量
- **同网关串行**：同一网关的请求必须串行执行，避免覆盖问题
- **动态命令ID**：为每个请求生成唯一的命令ID，确保响应正确匹配

### 架构组件

#### 1. GatewayLockManager（网关锁管理器）

```java
@Component
public class GatewayLockManager {
    // 为每个网关提供独立的锁
    private final ConcurrentHashMap<String, ReentrantLock> gatewayLocks;
    
    // 执行带锁的操作
    public <T> T executeWithLock(String gatewayCode, Supplier<T> operation);
}
```

**特性：**
- 使用`ReentrantLock`公平锁，确保请求按顺序处理
- 自动锁管理，包括获取、释放和清理
- 支持超时控制，避免死锁
- 提供锁状态查询和统计功能

#### 2. CommandIdGenerator（命令ID生成器）

```java
public class CommandIdGenerator {
    // 控制命令ID：1-99999
    private static final AtomicLong CONTROL_COMMAND_ID = new AtomicLong(1);
    
    // 读取命令ID：100000-999999
    private static final AtomicLong READ_COMMAND_ID = new AtomicLong(100000);
    
    public static Long generateReadCommandId();
    public static Long generateControlCommandId();
}
```

**特性：**
- 分离控制命令和读取命令的ID范围
- 原子操作保证线程安全
- 自动循环重置，避免ID耗尽

#### 3. 改进的实时数据查询流程

```java
public List<DataCodeValDTO> getRealTimeData(List<String> dataCodeList) {
    // 1. 按网关分组
    Map<String, List<String>> gatewayItemIdMap = strategyService.getGatewayItemIdMap(dataCodeList);
    
    // 2. 为每个网关串行执行
    List<DataCodeValDTO> allResults = new ArrayList<>();
    for (Map.Entry<String, List<String>> entry : gatewayItemIdMap.entrySet()) {
        String gatewayCode = entry.getKey();
        
        // 3. 使用网关锁保护
        List<DataCodeValDTO> gatewayResults = gatewayLockManager.executeWithLock(
            gatewayCode, 
            () -> strategyService.issueRealTimeDataMessage(singleGatewayMap)
        );
        
        allResults.addAll(gatewayResults);
    }
    
    return allResults;
}
```

## 实现细节

### 1. 网关锁机制

```java
// 获取网关专用锁
private ReentrantLock getLock(String gatewayCode) {
    return gatewayLocks.computeIfAbsent(gatewayCode, k -> {
        log.debug("为网关 {} 创建新的锁", gatewayCode);
        return new ReentrantLock(true); // 公平锁
    });
}

// 执行带锁操作
public <T> T executeWithLock(String gatewayCode, Supplier<T> operation, int timeoutSeconds) {
    ReentrantLock lock = getLock(gatewayCode);
    
    try {
        if (!lock.tryLock(timeoutSeconds, TimeUnit.SECONDS)) {
            throw new RuntimeException("获取网关锁超时，网关: " + gatewayCode);
        }
        
        return operation.get();
        
    } finally {
        if (lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
}
```

### 2. 动态命令ID生成

```java
// 在发送读取命令时生成唯一ID
public DefaultFuture sendReadMessage(String gatewayCode, String issueTopic, JSONObject msg) {
    Long commandId = CommandIdGenerator.generateReadCommandId();
    
    iMqttSender.sendToMqtt(issueTopic, 0, JSON.toJSONString(msg));
    return new DefaultFuture(commandId, gatewayCode, readTimeout, TimeoutTypeEnum.READ_TIMEOUT);
}

// 在构建MQTT命令时包含命令ID
JSONObject mqttCommJObject = new JSONObject();
mqttCommJObject.put("cmd", ControlPathEnum.CMD_READ.code());
mqttCommJObject.put("cid", commandId); // 添加命令ID
mqttCommJObject.put("tagID", tags);
```

### 3. 响应匹配改进

```java
// 处理读取响应时提取命令ID
if (cmd == 11) {
    Long cid = msgObj.getLong("cid");
    if (cid == null) {
        log.warn("读取响应缺少命令ID - 网关: {}", gatewayCode);
        cid = 99999L; // 向后兼容
    }
    
    Message mes = new Message();
    mes.setMessageId(cid);
    mes.setGatewayCode(gatewayCode);
    mqttResHandler.dealRead(mes);
}
```

## 性能优化

### 1. 锁粒度控制

- **网关级别锁**：每个网关独立锁，最小化锁竞争
- **公平锁**：确保请求按到达顺序处理，避免饥饿
- **超时机制**：防止死锁，提供故障恢复能力

### 2. 内存管理

```java
// 引用计数管理锁的生命周期
private void decrementRefCount(String gatewayCode) {
    lockRefCount.compute(gatewayCode, (k, v) -> {
        if (v == null || v <= 1) {
            gatewayLocks.remove(gatewayCode); // 自动清理
            return null;
        }
        return v - 1;
    });
}
```

### 3. 并发性能

- **不同网关并行**：网关A和网关B的请求可以同时执行
- **同网关串行**：网关A的多个请求按顺序执行
- **最小等待时间**：只有真正冲突的请求才会等待

## 使用示例

### 基本用法

```java
@Autowired
private CommonService commonService;

// 查询实时数据
List<String> dataCodeList = Arrays.asList("GATEWAY_001_POINT_01", "GATEWAY_002_POINT_01");
List<DataCodeValDTO> results = commonService.getRealTimeData(dataCodeList);
```

### 并发场景

```java
// 场景1：同时查询不同网关 - 并行执行
CompletableFuture<List<DataCodeValDTO>> future1 = CompletableFuture.supplyAsync(() -> 
    commonService.getRealTimeData(Arrays.asList("GATEWAY_001_POINT_01")));

CompletableFuture<List<DataCodeValDTO>> future2 = CompletableFuture.supplyAsync(() -> 
    commonService.getRealTimeData(Arrays.asList("GATEWAY_002_POINT_01")));

// 场景2：同时查询同一网关 - 串行执行
CompletableFuture<List<DataCodeValDTO>> future3 = CompletableFuture.supplyAsync(() -> 
    commonService.getRealTimeData(Arrays.asList("GATEWAY_001_POINT_02")));

CompletableFuture<List<DataCodeValDTO>> future4 = CompletableFuture.supplyAsync(() -> 
    commonService.getRealTimeData(Arrays.asList("GATEWAY_001_POINT_03")));
```

## 监控和调试

### 1. 锁状态查询

```java
@Autowired
private GatewayLockManager gatewayLockManager;

// 检查网关是否被锁定
boolean isLocked = gatewayLockManager.isLocked("GATEWAY_001");

// 获取等待队列长度
int queueLength = gatewayLockManager.getQueueLength("GATEWAY_001");

// 获取统计信息
String statistics = gatewayLockManager.getStatistics();
```

### 2. 日志监控

```
2024-08-11 10:30:15.123 INFO  - 获取网关锁成功 - 网关: GATEWAY_001, 等待时间: 1250ms
2024-08-11 10:30:15.456 DEBUG - 网关操作完成 - 网关: GATEWAY_001, 执行时间: 333ms
2024-08-11 10:30:15.789 INFO  - 实时数据查询完成 - 总网关数: 2, 总数据条数: 15
```

## 测试验证

### 单元测试

- `GatewayLockManagerTest`：验证锁机制的正确性
- `CommandIdGeneratorTest`：验证ID生成的唯一性

### 集成测试

- `RealTimeDataQueryTest`：验证完整的查询流程
- 并发测试：验证同网关串行、不同网关并行
- 压力测试：验证高并发场景下的稳定性

### 性能测试

```java
// 压力测试示例
@Test
void testHighConcurrencyLoad() {
    int totalRequests = 100;
    int gatewayCount = 5;
    
    // 提交大量并发请求
    // 验证：
    // 1. 所有请求都得到正确处理
    // 2. 没有响应覆盖问题
    // 3. 系统性能在可接受范围内
}
```

## 部署和配置

### 1. 配置参数

```yaml
# application.yml
mqtt:
  readTimeout: 3      # 读取超时时间（秒）
  controlTimeout: 5   # 控制超时时间（秒）

gateway:
  lock:
    defaultTimeout: 30  # 默认锁等待超时时间（秒）
    cleanupInterval: 5  # 锁清理间隔（分钟）
```

### 2. 监控指标

- 网关锁数量
- 平均等待时间
- 超时次数
- 并发请求数
- 响应成功率

## 总结

该解决方案通过以下方式解决了原有问题：

1. **消除命令ID冲突**：动态生成唯一命令ID
2. **防止响应覆盖**：网关级别的串行化处理
3. **保持高性能**：不同网关仍可并行处理
4. **提供监控能力**：完整的状态查询和统计功能
5. **确保系统稳定**：超时机制和异常处理

该方案在保证数据一致性的同时，最大化了系统的并发处理能力。
