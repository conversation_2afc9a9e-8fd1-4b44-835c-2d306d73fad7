package com.siact.control.enume;

/**
 * 超时类型枚举
 * 
 * <AUTHOR>
 * @create 2023/4/4
 */
public enum TimeoutTypeEnum {
    
    /**
     * 控制命令超时
     */
    CONTROL_TIMEOUT("控制命令超时"),
    
    /**
     * 数据读取超时
     */
    READ_TIMEOUT("数据读取超时");

    private final String message;

    TimeoutTypeEnum(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }
}


